// Background service worker for AI Sidebar Chrome Extension - Real-time Analysis

let sidebarActive = false;
const BACKEND_URL = 'https://5615c1cc-5dce-4a47-9951-8b7c0580112c.preview.emergentagent.com';

// Install event
chrome.runtime.onInstalled.addListener(() => {
    console.log('AI Sidebar Extension installed - Real-time analysis enabled');
    
    // Initialize storage
    chrome.storage.local.set({
        sidebarActive: false,
        currentSession: null,
        analysisHistory: [],
        autoAnalysisEnabled: true
    });
});

// Handle messages from content script and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'toggleSidebar') {
        handleToggleSidebar(sender.tab.id);
        sendResponse({ success: true });
    } else if (request.action === 'analyzeContent') {
        handleAnalyzeContent(request.data, sendResponse);
        return true; // Will respond asynchronously
    } else if (request.action === 'sendMessage') {
        handleSendMessage(request.data, sendResponse);
        return true; // Will respond asynchronously
    } else if (request.action === 'getHealth') {
        handleHealthCheck(sendResponse);
        return true; // Will respond asynchronously
    }
});

// Toggle sidebar in the active tab
async function handleToggleSidebar(tabId) {
    try {
        const results = await chrome.storage.local.get(['sidebarActive']);
        const newState = !results.sidebarActive;
        
        await chrome.storage.local.set({ sidebarActive: newState });
        
        // Send message to content script to toggle sidebar
        chrome.tabs.sendMessage(tabId, {
            action: 'toggleSidebar',
            active: newState
        });
        
        sidebarActive = newState;
        
        // If opening sidebar, trigger auto-analysis after a brief delay
        if (newState) {
            setTimeout(() => {
                chrome.tabs.sendMessage(tabId, {
                    action: 'autoAnalyze'
                });
            }, 800);
        }
        
    } catch (error) {
        console.error('Error toggling sidebar:', error);
    }
}

// Handle content analysis with enhanced error handling and caching
async function handleAnalyzeContent(data, sendResponse) {
    try {
        console.log('Analyzing content for:', data.url);
        
        // Add timestamp for request tracking
        const requestStart = Date.now();
        
        const response = await fetch(`${BACKEND_URL}/api/analyze-content`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        // Add processing time
        result.processingTime = Date.now() - requestStart;
        
        // Store analysis in history with enhanced metadata
        const storage = await chrome.storage.local.get(['analysisHistory']);
        const history = storage.analysisHistory || [];
        
        const analysisRecord = {
            ...result,
            auto: true,
            processingTime: result.processingTime
        };
        
        history.unshift(analysisRecord);
        
        // Keep only last 50 analyses
        if (history.length > 50) {
            history.splice(50);
        }
        
        await chrome.storage.local.set({ analysisHistory: history });
        
        console.log(`Analysis completed in ${result.processingTime}ms`);
        sendResponse({ success: true, data: result });
        
    } catch (error) {
        console.error('Error analyzing content:', error);
        
        // Enhanced error response
        sendResponse({ 
            success: false, 
            error: error.message,
            retryable: error.message.includes('HTTP error') || error.message.includes('Failed to fetch')
        });
    }
}

// Handle chat messages with enhanced context
async function handleSendMessage(data, sendResponse) {
    try {
        console.log('Sending chat message for session:', data.session_id);
        
        const response = await fetch(`${BACKEND_URL}/api/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        sendResponse({ success: true, data: result });
        
    } catch (error) {
        console.error('Error sending message:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// Handle health check with detailed status
async function handleHealthCheck(sendResponse) {
    try {
        const response = await fetch(`${BACKEND_URL}/api/health`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        // Add connection latency
        const pingStart = Date.now();
        await fetch(`${BACKEND_URL}/api/health`);
        const latency = Date.now() - pingStart;
        
        result.latency = latency;
        sendResponse({ success: true, data: result });
        
    } catch (error) {
        console.error('Error checking health:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// Enhanced tab update handling for automatic re-analysis
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        console.log('Page loaded:', tab.url);
        
        // Check if sidebar is active for this tab
        const storage = await chrome.storage.local.get(['sidebarActive']);
        
        if (storage.sidebarActive) {
            // Trigger auto-analysis after page load
            setTimeout(() => {
                chrome.tabs.sendMessage(tabId, {
                    action: 'autoAnalyze'
                }).catch(() => {
                    // Ignore errors (tab might not have content script loaded)
                });
            }, 1500);
        }
    }
});

// Handle extension icon click with enhanced behavior
chrome.action.onClicked.addListener(async (tab) => {
    await handleToggleSidebar(tab.id);
});

// Enhanced storage management
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local') {
        if (changes.autoAnalysisEnabled) {
            console.log('Auto-analysis setting changed:', changes.autoAnalysisEnabled.newValue);
        }
    }
});

// Periodic cleanup of old analysis data
setInterval(async () => {
    try {
        const storage = await chrome.storage.local.get(['analysisHistory']);
        const history = storage.analysisHistory || [];
        
        // Remove analyses older than 7 days
        const weekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        const cleanedHistory = history.filter(analysis => {
            const analysisDate = new Date(analysis.timestamp).getTime();
            return analysisDate > weekAgo;
        });
        
        if (cleanedHistory.length !== history.length) {
            await chrome.storage.local.set({ analysisHistory: cleanedHistory });
            console.log(`Cleaned up ${history.length - cleanedHistory.length} old analyses`);
        }
    } catch (error) {
        console.error('Error cleaning up storage:', error);
    }
}, 60 * 60 * 1000); // Run every hour

// Message port management for long-lived connections
const ports = new Map();

chrome.runtime.onConnect.addListener((port) => {
    if (port.name === 'ai-sidebar') {
        ports.set(port.sender.tab.id, port);
        
        port.onDisconnect.addListener(() => {
            ports.delete(port.sender.tab.id);
        });
        
        port.onMessage.addListener((message) => {
            // Handle long-lived connection messages
            if (message.type === 'keepAlive') {
                port.postMessage({ type: 'pong' });
            }
        });
    }
});