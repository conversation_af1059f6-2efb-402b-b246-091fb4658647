from fastapi import <PERSON><PERSON><PERSON>, API<PERSON><PERSON><PERSON>, HTTPException
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
from pathlib import Path
from pydantic import BaseModel, Field
from typing import List, Optional
import uuid
from datetime import datetime
import asyncio

# Import Gemini integration
from emergentintegrations.llm.chat import LlmChat, UserMessage

ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection
mongo_url = os.environ['MONGO_URL']
client = AsyncIOMotorClient(mongo_url)
db = client[os.environ['DB_NAME']]

# Create the main app without a prefix
app = FastAPI()

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")

# Gemini API key
GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY not found in environment variables")

# Define Models
class StatusCheck(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    client_name: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class StatusCheckCreate(BaseModel):
    client_name: str

# Chrome Extension Models
class PageContent(BaseModel):
    url: str
    title: str
    content: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class ContentAnalysisRequest(BaseModel):
    url: str
    title: str
    content: str
    analysis_type: str = "summary"  # summary, detailed, questions, translation

class ContentAnalysisResponse(BaseModel):
    id: str
    url: str
    title: str
    analysis: str
    analysis_type: str
    timestamp: datetime

class ChatMessage(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    session_id: str
    content: str
    role: str  # user, assistant
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class ChatRequest(BaseModel):
    session_id: str
    message: str
    context_url: Optional[str] = None
    context_content: Optional[str] = None

class ChatResponse(BaseModel):
    message_id: str
    session_id: str
    response: str
    timestamp: datetime

# Basic routes
@api_router.get("/")
async def root():
    return {"message": "AI Sidebar Chrome Extension Backend"}

@api_router.post("/status", response_model=StatusCheck)
async def create_status_check(input: StatusCheckCreate):
    status_dict = input.dict()
    status_obj = StatusCheck(**status_dict)
    _ = await db.status_checks.insert_one(status_obj.dict())
    return status_obj

@api_router.get("/status", response_model=List[StatusCheck])
async def get_status_checks():
    status_checks = await db.status_checks.find().to_list(1000)
    return [StatusCheck(**status_check) for status_check in status_checks]

# Chrome Extension Endpoints

@api_router.post("/analyze-content", response_model=ContentAnalysisResponse)
async def analyze_page_content(request: ContentAnalysisRequest):
    """Analyze webpage content using Gemini AI"""
    try:
        # Create a unique session for this analysis
        session_id = f"analysis_{uuid.uuid4()}"
        
        # Initialize Gemini chat
        chat = LlmChat(
            api_key=GEMINI_API_KEY,
            session_id=session_id,
            system_message="You are an intelligent content analyzer. Provide clear, structured, and helpful analysis of webpage content."
        ).with_model("gemini", "gemini-2.5-flash-preview-04-17")
        
        # Create analysis prompt based on type
        if request.analysis_type == "summary":
            prompt = f"""Please analyze this webpage content and provide a comprehensive summary:

URL: {request.url}
Title: {request.title}

Content:
{request.content}

Please provide:
1. A clear, concise summary (2-3 sentences)
2. Key points or main ideas (bullet points)
3. Important insights or takeaways
4. Content type classification (article, product page, documentation, etc.)"""

        elif request.analysis_type == "detailed":
            prompt = f"""Please provide a detailed analysis of this webpage content:

URL: {request.url}
Title: {request.title}

Content:
{request.content}

Please provide:
1. Detailed summary with context
2. Main themes and topics
3. Key arguments or information presented
4. Potential questions or areas for further exploration
5. Content quality and credibility assessment"""

        elif request.analysis_type == "questions":
            prompt = f"""Based on this webpage content, generate relevant questions that could help users understand or explore the topic further:

URL: {request.url}
Title: {request.title}

Content:
{request.content}

Please provide:
1. 5-7 thoughtful questions about the content
2. Questions should range from basic understanding to deeper analysis
3. Include questions that could lead to productive discussions"""

        else:  # Default to summary
            prompt = f"Please summarize this webpage content: {request.content}"

        # Send to Gemini
        user_message = UserMessage(text=prompt)
        analysis_result = await chat.send_message(user_message)
        
        # Save analysis to database
        analysis_id = str(uuid.uuid4())
        analysis_data = {
            "id": analysis_id,
            "url": request.url,
            "title": request.title,
            "content": request.content,
            "analysis": analysis_result,
            "analysis_type": request.analysis_type,
            "timestamp": datetime.utcnow()
        }
        
        await db.content_analyses.insert_one(analysis_data)
        
        return ContentAnalysisResponse(
            id=analysis_id,
            url=request.url,
            title=request.title,
            analysis=analysis_result,
            analysis_type=request.analysis_type,
            timestamp=analysis_data["timestamp"]
        )
        
    except Exception as e:
        logger.error(f"Error analyzing content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to analyze content: {str(e)}")

@api_router.post("/chat", response_model=ChatResponse)
async def chat_with_ai(request: ChatRequest):
    """Multi-turn chat with context awareness"""
    try:
        # Initialize Gemini chat with session context
        system_message = "You are an intelligent AI assistant integrated into a Chrome extension. You help users understand and interact with web content. Be helpful, concise, and context-aware."
        
        # Add context if provided
        if request.context_url and request.context_content:
            system_message += f"\n\nCurrent webpage context:\nURL: {request.context_url}\nContent: {request.context_content[:1000]}..."  # Limit context size
        
        chat = LlmChat(
            api_key=GEMINI_API_KEY,
            session_id=request.session_id,
            system_message=system_message
        ).with_model("gemini", "gemini-2.5-flash-preview-04-17")
        
        # Send user message
        user_message = UserMessage(text=request.message)
        ai_response = await chat.send_message(user_message)
        
        # Save messages to database
        message_id = str(uuid.uuid4())
        
        # Save user message
        user_msg_data = {
            "id": str(uuid.uuid4()),
            "session_id": request.session_id,
            "content": request.message,
            "role": "user",
            "timestamp": datetime.utcnow()
        }
        await db.chat_messages.insert_one(user_msg_data)
        
        # Save AI response
        ai_msg_data = {
            "id": message_id,
            "session_id": request.session_id,
            "content": ai_response,
            "role": "assistant",
            "timestamp": datetime.utcnow()
        }
        await db.chat_messages.insert_one(ai_msg_data)
        
        return ChatResponse(
            message_id=message_id,
            session_id=request.session_id,
            response=ai_response,
            timestamp=ai_msg_data["timestamp"]
        )
        
    except Exception as e:
        logger.error(f"Error in chat: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Chat failed: {str(e)}")

@api_router.get("/chat-history/{session_id}")
async def get_chat_history(session_id: str, limit: int = 50):
    """Get chat history for a session"""
    try:
        messages = await db.chat_messages.find(
            {"session_id": session_id}
        ).sort("timestamp", 1).limit(limit).to_list(limit)
        
        return [ChatMessage(**msg) for msg in messages]
        
    except Exception as e:
        logger.error(f"Error fetching chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch chat history: {str(e)}")

@api_router.get("/analyses")
async def get_recent_analyses(limit: int = 20):
    """Get recent content analyses"""
    try:
        analyses = await db.content_analyses.find().sort("timestamp", -1).limit(limit).to_list(limit)
        return [ContentAnalysisResponse(**analysis) for analysis in analyses]
        
    except Exception as e:
        logger.error(f"Error fetching analyses: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch analyses: {str(e)}")

@api_router.delete("/session/{session_id}")
async def clear_session(session_id: str):
    """Clear chat history for a session"""
    try:
        result = await db.chat_messages.delete_many({"session_id": session_id})
        return {"deleted_count": result.deleted_count}
        
    except Exception as e:
        logger.error(f"Error clearing session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clear session: {str(e)}")

# Health check endpoint
@api_router.get("/health")
async def health_check():
    """Health check endpoint for Chrome extension"""
    try:
        # Test Gemini connection
        test_chat = LlmChat(
            api_key=GEMINI_API_KEY,
            session_id="health_check",
            system_message="You are a test assistant."
        ).with_model("gemini", "gemini-2.5-flash-preview-04-17")
        
        test_message = UserMessage(text="Hello, this is a health check.")
        response = await test_chat.send_message(test_message)
        
        return {
            "status": "healthy",
            "gemini_connection": "ok",
            "database_connection": "ok",
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow()
        }

# Include the router in the main app
app.include_router(api_router)

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@app.on_event("shutdown")
async def shutdown_db_client():
    client.close()