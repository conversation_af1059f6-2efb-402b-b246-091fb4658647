<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            min-height: 400px;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .popup-container {
            padding: 20px;
        }
        
        .popup-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .popup-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .popup-header p {
            margin: 8px 0 0 0;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .popup-content {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            backdrop-filter: blur(10px);
            margin-bottom: 16px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feature-list li:before {
            content: "✨";
            font-size: 16px;
        }
        
        .popup-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.9);
            color: #4299e1;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            background: white;
        }
        
        .status-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
            font-size: 12px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #48bb78;
            display: inline-block;
        }
        
        .status-dot.disconnected {
            background: #f56565;
        }
        
        .keyboard-hint {
            font-size: 11px;
            opacity: 0.7;
            text-align: center;
            margin-top: 12px;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="popup-container">
        <div class="popup-header">
            <h1>🤖 AI Sidebar</h1>
            <p>Intelligent content analysis for any webpage</p>
        </div>
        
        <div class="popup-content">
            <ul class="feature-list">
                <li>Real-time content analysis</li>
                <li>Auto-triggered AI insights</li>
                <li>Context-aware chat assistant</li>
                <li>Seamless page navigation</li>
            </ul>
        </div>
        
        <div class="popup-actions">
            <button id="toggle-sidebar-btn" class="btn btn-primary">
                <span id="toggle-text">Open AI Sidebar</span>
            </button>
            <button id="analyze-current-btn" class="btn">
                🚀 Quick Analyze & Open
            </button>
        </div>
        
        <div class="status-section">
            <div class="status-item">
                <span>Backend Connection</span>
                <span id="backend-status">
                    <span class="status-dot" id="backend-dot"></span>
                    <span id="backend-text">Checking...</span>
                </span>
            </div>
            <div class="status-item">
                <span>AI Service</span>
                <span id="ai-status">
                    <span class="status-dot" id="ai-dot"></span>
                    <span id="ai-text">Checking...</span>
                </span>
            </div>
        </div>
        
        <div class="keyboard-hint">
            💡 Tip: Press Alt+S to toggle sidebar
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>