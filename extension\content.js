// Content script for AI Sidebar Chrome Extension - Real-time Analysis

let sidebarContainer = null;
let currentSessionId = null;
let isAnalyzing = false;
let isExtractingContent = false;
let lastAnalyzedUrl = null;
let autoAnalysisEnabled = true;

// Initialize content script
function initializeContentScript() {
    console.log('AI Sidebar content script loaded');
    
    // Generate session ID
    currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'toggleSidebar') {
            toggleSidebar(request.active);
            sendResponse({ success: true });
        } else if (request.action === 'autoAnalyze') {
            if (sidebarContainer && sidebarContainer.style.display !== 'none') {
                autoAnalyzeContent();
            }
            sendResponse({ success: true });
        }
    });
    
    // Add keyboard shortcut listener
    document.addEventListener('keydown', (e) => {
        // Alt + S to toggle sidebar
        if (e.altKey && e.key === 's') {
            e.preventDefault();
            chrome.runtime.sendMessage({ action: 'toggleSidebar' });
        }
    });

    // Listen for page content changes (for SPAs)
    const observer = new MutationObserver(() => {
        if (autoAnalysisEnabled && sidebarContainer && sidebarContainer.style.display !== 'none') {
            // Debounce the analysis to avoid too frequent calls
            clearTimeout(window.contentChangeTimeout);
            window.contentChangeTimeout = setTimeout(() => {
                if (window.location.href !== lastAnalyzedUrl) {
                    autoAnalyzeContent();
                }
            }, 2000);
        }
    });

    // Start observing changes
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: false
    });
}

// Toggle sidebar visibility
function toggleSidebar(active) {
    if (active) {
        showSidebar();
    } else {
        hideSidebar();
    }
}

// Show sidebar
function showSidebar() {
    if (sidebarContainer) {
        sidebarContainer.style.display = 'block';
        // Auto-analyze when sidebar is shown
        setTimeout(() => autoAnalyzeContent(), 500);
        return;
    }
    
    // Create sidebar container
    sidebarContainer = document.createElement('div');
    sidebarContainer.id = 'ai-sidebar-container';
    sidebarContainer.innerHTML = `
        <div class="ai-sidebar">
            <div class="ai-sidebar-header">
                <h3>🤖 AI Sidebar</h3>
                <div class="ai-sidebar-controls">
                    <div class="auto-analysis-status">
                        <span class="status-indicator">
                            <span class="pulse-dot"></span>
                            <span id="analysis-status-text">Analyzing...</span>
                        </span>
                    </div>
                    <button id="close-sidebar-btn" class="btn btn-close">✕</button>
                </div>
            </div>
            
            <div class="ai-sidebar-content">
                <div id="analysis-section" class="section">
                    <h4>📊 Real-time Analysis</h4>
                    <div id="analysis-controls">
                        <select id="analysis-type">
                            <option value="summary">Smart Summary</option>
                            <option value="detailed">Detailed Insights</option>
                            <option value="questions">Key Questions</option>
                        </select>
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-analysis-toggle" checked>
                            <span class="slider">Auto-analyze</span>
                        </label>
                    </div>
                    <div id="analysis-result" class="result-container">
                        <div class="auto-analysis-placeholder">
                            <div class="loading-animation">
                                <div class="loading-spinner"></div>
                                <p>Analyzing webpage content...</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="chat-section" class="section">
                    <h4>💬 AI Chat</h4>
                    <div class="chat-welcome">
                        <p>💡 Ask me anything about this page once analysis is complete!</p>
                    </div>
                    <div id="chat-messages" class="chat-messages"></div>
                    <div class="chat-input-container">
                        <input type="text" id="chat-input" placeholder="Ask me about this page..." disabled />
                        <button id="send-message-btn" class="btn btn-primary" disabled>
                            <span class="btn-text">Send</span>
                            <span class="btn-loading">⏳</span>
                        </button>
                    </div>
                </div>
                
                <div id="status-section" class="section">
                    <div id="connection-status" class="status-indicator">
                        <span class="status-dot"></span>
                        <span class="status-text">Connecting...</span>
                    </div>
                    <div class="page-info">
                        <div id="page-url" class="page-detail"></div>
                        <div id="content-stats" class="page-detail"></div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add to page
    document.body.appendChild(sidebarContainer);
    
    // Add event listeners
    setupEventListeners();
    
    // Check connection status
    checkConnectionStatus();
    
    // Auto-extract content and analyze when sidebar is first shown
    setTimeout(() => {
        extractPageContent();
        autoAnalyzeContent();
    }, 100);
}

// Hide sidebar
function hideSidebar() {
    if (sidebarContainer) {
        sidebarContainer.style.display = 'none';
    }
}

// Setup event listeners
function setupEventListeners() {
    // Close button
    document.getElementById('close-sidebar-btn').addEventListener('click', () => {
        chrome.runtime.sendMessage({ action: 'toggleSidebar' });
    });
    
    // Auto-analysis toggle
    document.getElementById('auto-analysis-toggle').addEventListener('change', (e) => {
        autoAnalysisEnabled = e.target.checked;
        const statusText = document.getElementById('analysis-status-text');
        if (autoAnalysisEnabled) {
            statusText.textContent = 'Auto-analysis enabled';
            // Trigger analysis if content exists
            if (window.extractedPageContent) {
                autoAnalyzeContent();
            }
        } else {
            statusText.textContent = 'Auto-analysis disabled';
        }
    });
    
    // Analysis type change - trigger re-analysis
    document.getElementById('analysis-type').addEventListener('change', () => {
        if (autoAnalysisEnabled && window.extractedPageContent) {
            autoAnalyzeContent();
        }
    });
    
    // Send message button
    document.getElementById('send-message-btn').addEventListener('click', () => {
        sendChatMessage();
    });
    
    // Chat input enter key
    document.getElementById('chat-input').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendChatMessage();
        }
    });
}

// Extract page content
function extractPageContent() {
    if (isExtractingContent) return;
    
    isExtractingContent = true;
    
    try {
        // Get page title
        const title = document.title || '';
        
        // Get page URL
        const url = window.location.href;
        
        // Extract main content
        let content = '';
        
        // Try to find main content areas
        const contentSelectors = [
            'main',
            'article',
            '[role="main"]',
            '.main-content',
            '.content',
            '.post-content',
            '.entry-content',
            'body'
        ];
        
        for (const selector of contentSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                content = element.innerText || element.textContent || '';
                break;
            }
        }
        
        // Clean up content
        content = content
            .replace(/\s+/g, ' ')  // Normalize whitespace
            .replace(/\n\s*\n/g, '\n')  // Remove excessive line breaks
            .trim();
        
        // Limit content length
        if (content.length > 5000) {
            content = content.substring(0, 5000) + '...';
        }
        
        // Store extracted content
        window.extractedPageContent = {
            url: url,
            title: title,
            content: content
        };
        
        // Update page info display
        updatePageInfo(url, content.length);
        
        console.log('Page content extracted:', {
            url: url,
            title: title,
            contentLength: content.length
        });
        
    } catch (error) {
        console.error('Error extracting page content:', error);
        window.extractedPageContent = {
            url: window.location.href,
            title: document.title || '',
            content: 'Error extracting content'
        };
    } finally {
        isExtractingContent = false;
    }
}

// Auto-analyze page content
async function autoAnalyzeContent() {
    if (!autoAnalysisEnabled || isAnalyzing) return;
    
    // Extract content if not already done
    if (!window.extractedPageContent || window.extractedPageContent.url !== window.location.href) {
        extractPageContent();
    }
    
    // Skip if same URL already analyzed
    if (lastAnalyzedUrl === window.location.href) return;
    
    isAnalyzing = true;
    lastAnalyzedUrl = window.location.href;
    
    const analysisResult = document.getElementById('analysis-result');
    const statusText = document.getElementById('analysis-status-text');
    const analysisType = document.getElementById('analysis-type').value;
    
    // Update UI to show analyzing state
    statusText.textContent = 'Analyzing content...';
    analysisResult.innerHTML = `
        <div class="auto-analysis-placeholder">
            <div class="loading-animation">
                <div class="loading-spinner"></div>
                <p>AI is analyzing this webpage...</p>
                <small>This may take a few seconds</small>
            </div>
        </div>
    `;
    
    try {
        const data = {
            ...window.extractedPageContent,
            analysis_type: analysisType
        };
        
        const response = await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'analyzeContent',
                data: data
            }, (response) => {
                if (response && response.success) {
                    resolve(response.data);
                } else {
                    reject(new Error(response ? response.error : 'No response'));
                }
            });
        });
        
        // Display analysis result with enhanced formatting
        const analysisHtml = response.analysis
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
            
        analysisResult.innerHTML = `
            <div class="analysis-content">
                <div class="analysis-meta">
                    <span class="analysis-type">${response.analysis_type}</span>
                    <span class="analysis-time">${new Date(response.timestamp).toLocaleTimeString()}</span>
                    <span class="auto-badge">Auto</span>
                </div>
                <div class="analysis-text">${analysisHtml}</div>
                <div class="analysis-actions">
                    <button class="btn-small" onclick="this.parentElement.parentElement.classList.toggle('expanded')">
                        <span class="expand-text">Show more</span>
                        <span class="collapse-text">Show less</span>
                    </button>
                </div>
            </div>
        `;
        
        // Update status
        statusText.textContent = 'Analysis complete';
        
        // Enable chat input
        document.getElementById('chat-input').disabled = false;
        document.getElementById('send-message-btn').disabled = false;
        document.getElementById('chat-input').placeholder = 'Ask me anything about this page...';
        
        // Hide welcome message
        const welcomeMsg = document.querySelector('.chat-welcome');
        if (welcomeMsg) {
            welcomeMsg.style.display = 'none';
        }
        
    } catch (error) {
        console.error('Error analyzing content:', error);
        analysisResult.innerHTML = `
            <div class="error-message">
                <div class="error-icon">⚠️</div>
                <div class="error-text">
                    <strong>Analysis failed</strong><br>
                    <small>${error.message}</small>
                </div>
                <button class="btn-small retry-btn" onclick="autoAnalyzeContent()">
                    🔄 Retry
                </button>
            </div>
        `;
        statusText.textContent = 'Analysis failed';
    } finally {
        isAnalyzing = false;
    }
}

// Update page info display
function updatePageInfo(url, contentLength) {
    const pageUrl = document.getElementById('page-url');
    const contentStats = document.getElementById('content-stats');
    
    if (pageUrl) {
        const displayUrl = url.length > 50 ? url.substring(0, 47) + '...' : url;
        pageUrl.innerHTML = `<strong>URL:</strong> ${displayUrl}`;
    }
    
    if (contentStats) {
        const words = Math.round(contentLength / 5); // Rough word count
        contentStats.innerHTML = `<strong>Content:</strong> ~${words} words, ${contentLength} chars`;
    }
}

// Send chat message (enhanced for real-time context)
async function sendChatMessage() {
    const chatInput = document.getElementById('chat-input');
    const sendBtn = document.getElementById('send-message-btn');
    const chatMessages = document.getElementById('chat-messages');
    
    const message = chatInput.value.trim();
    if (!message) return;
    
    // Add user message to chat
    addChatMessage('user', message);
    
    // Clear input and disable send button
    chatInput.value = '';
    sendBtn.classList.add('loading');
    chatInput.disabled = true;
    
    try {
        const data = {
            session_id: currentSessionId,
            message: message,
            context_url: window.extractedPageContent?.url || null,
            context_content: window.extractedPageContent?.content || null
        };
        
        const response = await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'sendMessage',
                data: data
            }, (response) => {
                if (response && response.success) {
                    resolve(response.data);
                } else {
                    reject(new Error(response ? response.error : 'No response'));
                }
            });
        });
        
        // Add AI response to chat
        addChatMessage('assistant', response.response);
        
    } catch (error) {
        console.error('Error sending message:', error);
        addChatMessage('error', `❌ Error: ${error.message}`);
    } finally {
        sendBtn.classList.remove('loading');
        chatInput.disabled = false;
        chatInput.focus();
    }
}

// Add message to chat (enhanced with better formatting)
function addChatMessage(role, content) {
    const chatMessages = document.getElementById('chat-messages');
    const messageElement = document.createElement('div');
    messageElement.className = `chat-message ${role}`;
    
    const timestamp = new Date().toLocaleTimeString();
    
    // Enhanced content formatting
    const formattedContent = content
        .replace(/\n/g, '<br>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    messageElement.innerHTML = `
        <div class="message-content">${formattedContent}</div>
        <div class="message-time">${timestamp}</div>
    `;
    
    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Check connection status
async function checkConnectionStatus() {
    const statusIndicator = document.getElementById('connection-status');
    const statusDot = statusIndicator.querySelector('.status-dot');
    const statusText = statusIndicator.querySelector('.status-text');
    
    try {
        const response = await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'getHealth'
            }, (response) => {
                if (response && response.success) {
                    resolve(response.data);
                } else {
                    reject(new Error(response ? response.error : 'No response'));
                }
            });
        });
        
        if (response.status === 'healthy') {
            statusDot.className = 'status-dot connected';
            statusText.textContent = 'AI Ready';
        } else {
            statusDot.className = 'status-dot disconnected';
            statusText.textContent = 'AI Unavailable';
        }
        
    } catch (error) {
        console.error('Error checking connection:', error);
        statusDot.className = 'status-dot disconnected';
        statusText.textContent = 'Connection Failed';
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
    initializeContentScript();
}