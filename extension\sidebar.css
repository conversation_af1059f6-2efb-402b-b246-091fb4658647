/* AI Sidebar Chrome Extension Styles - Real-time Analysis Enhanced */

#ai-sidebar-container {
    position: fixed;
    top: 0;
    right: 0;
    width: 400px;
    height: 100vh;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out;
}

.ai-sidebar {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.ai-sidebar-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-sidebar-header h3 {
    margin: 0;
    color: white;
    font-size: 18px;
    font-weight: 600;
}

.ai-sidebar-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.auto-analysis-status {
    display: flex;
    align-items: center;
    color: white;
    font-size: 12px;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
}

.pulse-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #48bb78;
    margin-right: 6px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.ai-sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
}

.section {
    margin-bottom: 24px;
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.section h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn {
    background: #4299e1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    background: #3182ce;
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: linear-gradient(135deg, #4299e1, #667eea);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #3182ce, #5a67d8);
}

.btn-close {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 8px;
    font-size: 16px;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
    background: #e2e8f0;
    color: #4a5568;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-small:hover {
    background: #cbd5e0;
}

.btn.loading .btn-text {
    display: none;
}

.btn.loading .btn-loading {
    display: inline;
}

.btn-loading {
    display: none;
}

#analysis-controls {
    margin-bottom: 12px;
    display: flex;
    gap: 12px;
    align-items: center;
}

#analysis-controls select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.toggle-switch {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #718096;
    cursor: pointer;
}

.toggle-switch input {
    appearance: none;
    width: 36px;
    height: 20px;
    background: #cbd5e0;
    border-radius: 10px;
    position: relative;
    cursor: pointer;
    transition: background 0.3s;
}

.toggle-switch input:checked {
    background: #4299e1;
}

.toggle-switch input::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform 0.3s;
}

.toggle-switch input:checked::before {
    transform: translateX(16px);
}

.result-container {
    min-height: 150px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0;
    background: #f8f9fa;
    overflow: hidden;
}

.auto-analysis-placeholder {
    padding: 24px;
    text-align: center;
}

.loading-animation {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #4299e1;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.analysis-content {
    padding: 16px;
    line-height: 1.6;
    max-height: 300px;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.analysis-content.expanded {
    max-height: none;
    overflow: visible;
}

.analysis-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 12px;
    color: #718096;
    flex-wrap: wrap;
    gap: 8px;
}

.analysis-type {
    background: #e2e8f0;
    padding: 2px 8px;
    border-radius: 4px;
    text-transform: capitalize;
}

.auto-badge {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
}

.analysis-text {
    color: #2d3748;
    line-height: 1.6;
}

.analysis-actions {
    margin-top: 12px;
    display: flex;
    justify-content: flex-end;
}

.expand-text {
    display: inline;
}

.collapse-text {
    display: none;
}

.analysis-content.expanded .expand-text {
    display: none;
}

.analysis-content.expanded .collapse-text {
    display: inline;
}

.chat-welcome {
    background: linear-gradient(135deg, #e2e8f0, #edf2f7);
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 12px;
    text-align: center;
    color: #4a5568;
    font-size: 14px;
}

.chat-messages {
    height: 200px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 8px;
    background: white;
    margin-bottom: 12px;
}

.chat-message {
    margin-bottom: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    max-width: 90%;
    animation: fadeInUp 0.3s ease-out;
}

.chat-message.user {
    background: #4299e1;
    color: white;
    margin-left: auto;
    text-align: right;
}

.chat-message.assistant {
    background: #f7fafc;
    color: #2d3748;
    border: 1px solid #e2e8f0;
}

.chat-message.error {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.message-content {
    margin-bottom: 4px;
    line-height: 1.4;
    word-wrap: break-word;
}

.message-time {
    font-size: 11px;
    opacity: 0.6;
}

.chat-input-container {
    display: flex;
    gap: 8px;
}

#chat-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

#chat-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

#chat-input:disabled {
    background: #f7fafc;
    cursor: not-allowed;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #718096;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #cbd5e0;
    transition: background-color 0.3s ease;
}

.status-dot.connected {
    background: #48bb78;
    box-shadow: 0 0 0 2px rgba(72, 187, 120, 0.2);
}

.status-dot.disconnected {
    background: #f56565;
    box-shadow: 0 0 0 2px rgba(245, 101, 101, 0.2);
}

.page-info {
    margin-top: 8px;
    font-size: 11px;
    color: #718096;
}

.page-detail {
    margin-bottom: 4px;
    word-break: break-all;
}

.error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #feb2b2;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.error-icon {
    font-size: 24px;
}

.retry-btn {
    margin-top: 8px;
    background: #c53030;
    color: white;
}

.retry-btn:hover {
    background: #9c2626;
}

/* Responsive adjustments */
@media (max-width: 500px) {
    #ai-sidebar-container {
        width: 100%;
        height: 100vh;
    }
}

/* Scrollbar styling */
.ai-sidebar-content::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.ai-sidebar-content::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.ai-sidebar-content::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.ai-sidebar-content::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Animation for new messages */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced status animations */
.auto-analysis-status .pulse-dot.analyzing {
    background: #f6ad55;
    animation: pulse 1s infinite;
}

.auto-analysis-status .pulse-dot.complete {
    background: #48bb78;
    animation: none;
}

.auto-analysis-status .pulse-dot.error {
    background: #f56565;
    animation: pulse 0.5s infinite;
}