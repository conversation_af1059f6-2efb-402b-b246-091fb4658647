#!/bin/bash

# Chrome Extension Structure Test Script

echo "🔍 AI Sidebar Chrome Extension - Structure Test"
echo "============================================="

# Check if extension directory exists
if [ ! -d "/app/extension" ]; then
    echo "❌ Extension directory not found"
    exit 1
fi

echo "✅ Extension directory found"

# Check required files
required_files=(
    "manifest.json"
    "background.js"
    "content.js"
    "sidebar.css"
    "popup.html"
    "popup.js"
    "README.md"
)

echo ""
echo "📁 Checking required files:"
for file in "${required_files[@]}"; do
    if [ -f "/app/extension/$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - Missing"
    fi
done

# Check icons directory
if [ -d "/app/extension/icons" ]; then
    echo "✅ icons/ directory"
else
    echo "❌ icons/ directory - Missing"
fi

# Validate manifest.json
echo ""
echo "📋 Validating manifest.json:"
if [ -f "/app/extension/manifest.json" ]; then
    # Check for required fields
    if grep -q "\"manifest_version\": 3" /app/extension/manifest.json; then
        echo "✅ Manifest version 3"
    else
        echo "❌ Manifest version 3 - Missing or incorrect"
    fi
    
    if grep -q "\"name\":" /app/extension/manifest.json; then
        echo "✅ Extension name"
    else
        echo "❌ Extension name - Missing"
    fi
    
    if grep -q "\"permissions\":" /app/extension/manifest.json; then
        echo "✅ Permissions declared"
    else
        echo "❌ Permissions - Missing"
    fi
    
    if grep -q "\"content_scripts\":" /app/extension/manifest.json; then
        echo "✅ Content scripts declared"
    else
        echo "❌ Content scripts - Missing"
    fi
    
    if grep -q "\"background\":" /app/extension/manifest.json; then
        echo "✅ Background script declared"
    else
        echo "❌ Background script - Missing"
    fi
else
    echo "❌ manifest.json not found"
fi

# Check file sizes (basic validation)
echo ""
echo "📊 File size validation:"
for file in "${required_files[@]}"; do
    if [ -f "/app/extension/$file" ]; then
        size=$(stat -c%s "/app/extension/$file")
        if [ $size -gt 100 ]; then
            echo "✅ $file ($size bytes)"
        else
            echo "⚠️ $file ($size bytes) - File seems too small"
        fi
    fi
done

# Check backend connection URL
echo ""
echo "🔗 Backend connection check:"
if grep -q "5615c1cc-5dce-4a47-9951-8b7c0580112c.preview.emergentagent.com" /app/extension/background.js; then
    echo "✅ Backend URL configured"
else
    echo "❌ Backend URL not found or incorrect"
fi

# Summary
echo ""
echo "📋 Installation Instructions:"
echo "1. Open Chrome and go to chrome://extensions/"
echo "2. Enable 'Developer mode' (toggle in top right)"
echo "3. Click 'Load unpacked'"
echo "4. Select the /app/extension folder"
echo "5. The AI Sidebar extension should appear in your extensions list"
echo ""
echo "🎯 Usage:"
echo "- Click extension icon to open popup"
echo "- Click 'Open AI Sidebar' to show sidebar"
echo "- Use Alt+S keyboard shortcut to toggle sidebar"
echo "- Analyze any webpage content with AI"
echo ""
echo "✨ Extension structure validation complete!"