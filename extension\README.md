# AI Sidebar Chrome Extension

## 🚀 Installation & Setup

### 1. Load Extension in Chrome

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the `/app/extension` folder
5. The AI Sidebar extension should now appear in your extensions list

### 2. Backend Setup

The extension requires a local backend server to be running:

```bash
# Make sure backend is running
sudo supervisorctl status backend

# Backend should be running on port 8001
# Health check: https://your-backend-url.com/api/health
```

## 🎯 Features

### ✨ Real-time Intelligence
- **🚀 Automatic Content Analysis**: AI instantly analyzes webpage content when sidebar opens
- **⚡ Real-time Insights**: No manual button clicks - analysis happens automatically
- **🔄 Smart Navigation**: Auto-analyzes new pages as you browse
- **🎛️ Analysis Control**: Toggle auto-analysis on/off as needed

### 🤖 AI-Powered Analysis
- **📊 Multiple Analysis Types**: Summary, detailed analysis, question generation
- **🧠 Context Awareness**: AI understands current webpage content
- **💬 Intelligent Chat**: Multi-turn conversations about page content
- **📝 Session Management**: Persistent chat history per browsing session

### 🎨 Beautiful Interface
- **🌟 Modern Design**: Glass-morphism sidebar with smooth animations
- **📱 Responsive Layout**: Works perfectly on different screen sizes
- **⚡ Real-time Status**: Live connection monitoring and analysis progress
- **⌨️ Keyboard Shortcuts**: Alt+S to toggle sidebar instantly

### 🔧 Smart Automation
- **🎯 Auto-trigger**: Analysis starts automatically when sidebar opens
- **🔄 Page Detection**: Detects navigation and re-analyzes content
- **⏱️ Smart Timing**: Debounced analysis to avoid excessive API calls
- **💾 History Management**: Automatic cleanup of old analysis data

## 📋 Usage

### 🚀 Real-time Analysis Workflow

1. **Instant Setup**: Click extension icon or press `Alt+S`
2. **Automatic Analysis**: AI immediately starts analyzing the current webpage
3. **Live Insights**: Get instant summaries, insights, or questions
4. **Smart Chat**: Ask questions about the page content
5. **Seamless Browsing**: Navigate to new pages and get auto-analysis

### ⚡ Auto-Analysis Features

**When Sidebar Opens:**
- ✅ Automatically extracts webpage content
- ✅ Runs AI analysis based on selected type (Summary/Detailed/Questions)
- ✅ Displays results in real-time
- ✅ Enables contextual chat

**When You Navigate:**
- ✅ Detects page changes automatically
- ✅ Re-analyzes new content
- ✅ Updates chat context
- ✅ Maintains session continuity

**Smart Controls:**
- 🎛️ Toggle auto-analysis on/off
- 📊 Switch analysis types (triggers re-analysis)
- 💬 Chat remains context-aware
- ⚡ Real-time status indicators

### 🎯 Popup Quick Actions

- **"🚀 Quick Analyze & Open"**: Instantly opens sidebar with auto-analysis
- **Connection Status**: Live backend and AI service monitoring
- **Page Information**: Current page details and analysis readiness

### 🎨 Enhanced User Experience

**No More Manual Clicks:**
- ❌ Old: Click "Analyze Page" button
- ✅ New: Automatic analysis on sidebar open

**Smart Timing:**
- ⏱️ Analysis starts 100ms after sidebar opens
- 🔄 Re-analysis triggers 2 seconds after page changes
- ⚡ Debounced to prevent excessive API calls

**Visual Feedback:**
- 🔄 Pulsing dot shows analysis in progress
- ✅ Status updates when analysis completes
- ❌ Clear error states with retry options

## 🔧 Technical Details

### Extension Structure
```
extension/
├── manifest.json          # Extension configuration
├── background.js          # Service worker
├── content.js            # Page injection script
├── sidebar.css           # Sidebar styles
├── popup.html           # Extension popup
├── popup.js             # Popup functionality
└── icons/               # Extension icons
```

### API Endpoints Used
- `GET /api/health` - Health check
- `POST /api/analyze-content` - Content analysis
- `POST /api/chat` - AI chat
- `GET /api/chat-history/{session_id}` - Chat history

### Permissions Required
- `activeTab` - Access current tab content
- `storage` - Store extension data
- `scripting` - Inject content scripts
- `tabs` - Tab management

## 🚨 Troubleshooting

### Extension Not Working
1. Check if backend is running: `sudo supervisorctl status backend`
2. Verify backend URL in `background.js`
3. Check browser console for errors
4. Reload extension in `chrome://extensions/`

### Connection Issues
1. Check popup status indicators
2. Verify CORS settings in backend
3. Check network tab for failed requests
4. Ensure Gemini API key is configured

### Content Not Extracted
1. Try refreshing the page
2. Check if page has anti-bot protection
3. Verify content script injection
4. Look for CSP (Content Security Policy) blocks

## 📱 Keyboard Shortcuts

- `Alt + S` - Toggle sidebar
- `Enter` - Send chat message (when input focused)
- `Escape` - Close sidebar (when sidebar focused)

## 🎨 Customization

The extension uses CSS variables for easy theming:
- Modify `sidebar.css` for visual changes
- Update `popup.html` for popup layout
- Adjust `content.js` for behavior changes

## 🔒 Privacy & Security

- Content is only sent to AI when explicitly requested
- No persistent storage of sensitive data
- Local backend processing
- Secure API communication

## 🚀 Performance

- Lightweight content script injection
- Efficient DOM content extraction
- Optimized API communication
- Minimal memory footprint

---

**Ready to use!** The AI Sidebar Chrome Extension is now fully functional with Gemini AI integration.