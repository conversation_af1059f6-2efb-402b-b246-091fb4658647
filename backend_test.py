import unittest
import asyncio
import json
import uuid
import requests
import os
from datetime import datetime

# Get the backend URL from the frontend .env file
BACKEND_URL = "https://5615c1cc-5dce-4a47-9951-8b7c0580112c.preview.emergentagent.com"
API_BASE_URL = f"{BACKEND_URL}/api"

class TestGeminiIntegration(unittest.TestCase):
    """Test the Gemini AI integration in the Chrome Extension backend"""

    def setUp(self):
        """Set up test variables"""
        self.session_id = str(uuid.uuid4())
        self.sample_content = """
        Artificial Intelligence (AI) is transforming how we interact with technology. 
        From virtual assistants to recommendation systems, AI is becoming increasingly 
        integrated into our daily lives. Machine learning, a subset of AI, enables 
        systems to learn from data and improve over time without explicit programming.
        
        Large language models like GPT-4 and Gemini represent significant advancements 
        in natural language processing, capable of generating human-like text, translating 
        languages, and answering questions with remarkable accuracy.
        """
        self.sample_url = "https://example.com/ai-article"
        self.sample_title = "Understanding Artificial Intelligence"

    def test_health_check(self):
        """Test the health check endpoint to verify Gemini connection"""
        response = requests.get(f"{API_BASE_URL}/health")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertEqual(data["status"], "healthy")
        self.assertEqual(data["gemini_connection"], "ok")
        self.assertEqual(data["database_connection"], "ok")
        self.assertIn("timestamp", data)
        
        print("✅ Health check passed - Gemini connection verified")

    def test_content_analysis_summary(self):
        """Test content analysis with summary type"""
        payload = {
            "url": self.sample_url,
            "title": self.sample_title,
            "content": self.sample_content,
            "analysis_type": "summary"
        }
        
        response = requests.post(f"{API_BASE_URL}/analyze-content", json=payload)
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("id", data)
        self.assertEqual(data["url"], self.sample_url)
        self.assertEqual(data["title"], self.sample_title)
        self.assertIn("analysis", data)
        self.assertEqual(data["analysis_type"], "summary")
        self.assertIn("timestamp", data)
        
        # Verify analysis content is meaningful
        self.assertTrue(len(data["analysis"]) > 50)
        self.assertIn("AI", data["analysis"])
        
        print("✅ Content analysis (summary) passed")
        return data["id"]  # Return ID for later use

    def test_content_analysis_detailed(self):
        """Test content analysis with detailed type"""
        payload = {
            "url": self.sample_url,
            "title": self.sample_title,
            "content": self.sample_content,
            "analysis_type": "detailed"
        }
        
        response = requests.post(f"{API_BASE_URL}/analyze-content", json=payload)
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("id", data)
        self.assertEqual(data["url"], self.sample_url)
        self.assertEqual(data["title"], self.sample_title)
        self.assertIn("analysis", data)
        self.assertEqual(data["analysis_type"], "detailed")
        
        # Verify detailed analysis is more comprehensive
        self.assertTrue(len(data["analysis"]) > 100)
        
        print("✅ Content analysis (detailed) passed")

    def test_content_analysis_questions(self):
        """Test content analysis with questions type"""
        payload = {
            "url": self.sample_url,
            "title": self.sample_title,
            "content": self.sample_content,
            "analysis_type": "questions"
        }
        
        response = requests.post(f"{API_BASE_URL}/analyze-content", json=payload)
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("id", data)
        self.assertEqual(data["analysis_type"], "questions")
        
        # Verify questions are generated
        self.assertIn("?", data["analysis"])
        
        print("✅ Content analysis (questions) passed")

    def test_chat_without_context(self):
        """Test basic chat without webpage context"""
        payload = {
            "session_id": self.session_id,
            "message": "What is artificial intelligence?",
            "context_url": None,
            "context_content": None
        }
        
        response = requests.post(f"{API_BASE_URL}/chat", json=payload)
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("message_id", data)
        self.assertEqual(data["session_id"], self.session_id)
        self.assertIn("response", data)
        self.assertIn("timestamp", data)
        
        # Verify response is meaningful
        self.assertTrue(len(data["response"]) > 50)
        self.assertIn("intelligence", data["response"].lower())
        
        print("✅ Chat without context passed")
        return data["message_id"]

    def test_chat_with_context(self):
        """Test chat with webpage context"""
        payload = {
            "session_id": self.session_id,
            "message": "Summarize the main points from this webpage",
            "context_url": self.sample_url,
            "context_content": self.sample_content
        }
        
        response = requests.post(f"{API_BASE_URL}/chat", json=payload)
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("message_id", data)
        self.assertEqual(data["session_id"], self.session_id)
        self.assertIn("response", data)
        
        # Verify response references the context
        self.assertIn("AI", data["response"])
        
        print("✅ Chat with context passed")

    def test_multi_turn_conversation(self):
        """Test multi-turn conversation in the same session"""
        # First message
        payload1 = {
            "session_id": self.session_id,
            "message": "What are large language models?",
            "context_url": None,
            "context_content": None
        }
        
        response1 = requests.post(f"{API_BASE_URL}/chat", json=payload1)
        self.assertEqual(response1.status_code, 200)
        
        # Follow-up message that references the first
        payload2 = {
            "session_id": self.session_id,
            "message": "What are their limitations?",
            "context_url": None,
            "context_content": None
        }
        
        response2 = requests.post(f"{API_BASE_URL}/chat", json=payload2)
        self.assertEqual(response2.status_code, 200)
        
        data = response2.json()
        
        # Verify the response addresses limitations of language models
        self.assertIn("limitation", data["response"].lower())
        
        print("✅ Multi-turn conversation passed")

    def test_chat_history(self):
        """Test retrieving chat history"""
        # First ensure we have some chat history
        self.test_chat_without_context()
        self.test_chat_with_context()
        
        # Now retrieve the history
        response = requests.get(f"{API_BASE_URL}/chat-history/{self.session_id}")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIsInstance(data, list)
        self.assertGreaterEqual(len(data), 4)  # At least 4 messages (2 user, 2 assistant)
        
        # Verify structure of messages
        for message in data:
            self.assertIn("id", message)
            self.assertEqual(message["session_id"], self.session_id)
            self.assertIn("content", message)
            self.assertIn("role", message)
            self.assertIn("timestamp", message)
        
        print("✅ Chat history retrieval passed")

    def test_clear_session(self):
        """Test clearing a chat session"""
        # First ensure we have some chat history
        self.test_chat_without_context()
        
        # Now clear the session
        response = requests.delete(f"{API_BASE_URL}/session/{self.session_id}")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("deleted_count", data)
        self.assertGreaterEqual(data["deleted_count"], 2)  # At least 2 messages deleted
        
        # Verify history is empty
        response = requests.get(f"{API_BASE_URL}/chat-history/{self.session_id}")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertEqual(len(data), 0)
        
        print("✅ Clear session passed")

    def test_recent_analyses(self):
        """Test retrieving recent analyses"""
        # First ensure we have some analyses
        self.test_content_analysis_summary()
        self.test_content_analysis_detailed()
        
        # Now retrieve recent analyses
        response = requests.get(f"{API_BASE_URL}/analyses")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIsInstance(data, list)
        self.assertGreaterEqual(len(data), 2)  # At least 2 analyses
        
        # Verify structure of analyses
        for analysis in data:
            self.assertIn("id", analysis)
            self.assertIn("url", analysis)
            self.assertIn("title", analysis)
            self.assertIn("analysis", analysis)
            self.assertIn("analysis_type", analysis)
            self.assertIn("timestamp", analysis)
        
        print("✅ Recent analyses retrieval passed")

def run_tests():
    """Run all tests"""
    print(f"Testing backend API at: {API_BASE_URL}")
    print("=" * 50)
    
    # Create test suite
    suite = unittest.TestSuite()
    suite.addTest(TestGeminiIntegration("test_health_check"))
    suite.addTest(TestGeminiIntegration("test_content_analysis_summary"))
    suite.addTest(TestGeminiIntegration("test_content_analysis_detailed"))
    suite.addTest(TestGeminiIntegration("test_content_analysis_questions"))
    suite.addTest(TestGeminiIntegration("test_chat_without_context"))
    suite.addTest(TestGeminiIntegration("test_chat_with_context"))
    suite.addTest(TestGeminiIntegration("test_multi_turn_conversation"))
    suite.addTest(TestGeminiIntegration("test_chat_history"))
    suite.addTest(TestGeminiIntegration("test_clear_session"))
    suite.addTest(TestGeminiIntegration("test_recent_analyses"))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 50)
    if result.wasSuccessful():
        print("✅ All tests passed successfully!")
    else:
        print(f"❌ {len(result.failures)} tests failed.")
        for failure in result.failures:
            print(f"Failed: {failure[0]}")
            print(f"Error: {failure[1]}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_tests()