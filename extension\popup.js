// Popup script for AI Sidebar Chrome Extension - Real-time Analysis

let currentTab = null;
let sidebarActive = false;

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
    console.log('AI Sidebar popup loaded - Real-time analysis enabled');
    
    // Get current tab
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    currentTab = tabs[0];
    
    // Check current sidebar state
    const storage = await chrome.storage.local.get(['sidebarActive']);
    sidebarActive = storage.sidebarActive || false;
    updateToggleButton();
    
    // Setup event listeners
    setupEventListeners();
    
    // Check connection status
    checkConnectionStatus();
    
    // Show page info
    showPageInfo();
});

// Setup event listeners
function setupEventListeners() {
    // Toggle sidebar button
    document.getElementById('toggle-sidebar-btn').addEventListener('click', () => {
        toggleSidebar();
    });
    
    // Quick analyze button - now triggers auto-analysis
    document.getElementById('analyze-current-btn').addEventListener('click', () => {
        quickAnalyzeAndOpen();
    });
}

// Toggle sidebar
async function toggleSidebar() {
    try {
        const response = await chrome.runtime.sendMessage({
            action: 'toggleSidebar'
        });
        
        if (response.success) {
            sidebarActive = !sidebarActive;
            updateToggleButton();
            
            // Show success message
            if (sidebarActive) {
                showSuccessMessage('Sidebar opened - Auto-analyzing...');
            } else {
                showSuccessMessage('Sidebar closed');
            }
            
            // Close popup after toggling
            setTimeout(() => {
                window.close();
            }, 500);
        }
    } catch (error) {
        console.error('Error toggling sidebar:', error);
        showError('Failed to toggle sidebar');
    }
}

// Update toggle button text and state
function updateToggleButton() {
    const toggleBtn = document.getElementById('toggle-sidebar-btn');
    const toggleText = document.getElementById('toggle-text');
    
    if (sidebarActive) {
        toggleText.textContent = 'Close AI Sidebar';
        toggleBtn.style.background = 'rgba(245, 101, 101, 0.8)';
        toggleBtn.style.color = 'white';
    } else {
        toggleText.textContent = 'Open AI Sidebar';
        toggleBtn.style.background = 'rgba(255, 255, 255, 0.9)';
        toggleBtn.style.color = '#4299e1';
    }
}

// Quick analyze and open sidebar
async function quickAnalyzeAndOpen() {
    const analyzeBtn = document.getElementById('analyze-current-btn');
    
    try {
        // Show loading state
        analyzeBtn.classList.add('loading');
        analyzeBtn.textContent = '🚀 Opening & Auto-analyzing...';
        
        // Open sidebar first (which will trigger auto-analysis)
        if (!sidebarActive) {
            const response = await chrome.runtime.sendMessage({
                action: 'toggleSidebar'
            });
            
            if (response.success) {
                sidebarActive = true;
                showSuccessMessage('Sidebar opened with auto-analysis!');
                
                // Close popup
                setTimeout(() => {
                    window.close();
                }, 800);
            }
        } else {
            // Sidebar already open, just trigger analysis
            showSuccessMessage('Auto-analysis triggered!');
            setTimeout(() => {
                window.close();
            }, 500);
        }
        
    } catch (error) {
        console.error('Error with quick analyze:', error);
        showError('Failed to open sidebar');
        analyzeBtn.textContent = 'Quick Analyze & Open';
        analyzeBtn.classList.remove('loading');
    }
}

// Show page information
function showPageInfo() {
    if (currentTab) {
        // Update popup with current page info
        const domain = new URL(currentTab.url).hostname;
        const title = currentTab.title;
        
        // Add page info section if not exists
        const existingInfo = document.querySelector('.page-info-section');
        if (!existingInfo) {
            const pageInfoHTML = `
                <div class="page-info-section">
                    <div class="page-info-header">
                        <h3>Current Page</h3>
                    </div>
                    <div class="page-details">
                        <div class="page-title">${title.length > 40 ? title.substring(0, 40) + '...' : title}</div>
                        <div class="page-domain">${domain}</div>
                    </div>
                </div>
            `;
            
            // Insert before status section
            const statusSection = document.querySelector('.status-section');
            statusSection.insertAdjacentHTML('beforebegin', pageInfoHTML);
        }
    }
}

// Check connection status with enhanced feedback
async function checkConnectionStatus() {
    try {
        const response = await chrome.runtime.sendMessage({
            action: 'getHealth'
        });
        
        if (response.success) {
            const health = response.data;
            
            // Update backend status
            const backendDot = document.getElementById('backend-dot');
            const backendText = document.getElementById('backend-text');
            
            if (health.status === 'healthy') {
                backendDot.classList.remove('disconnected');
                backendText.textContent = `Connected (${health.latency || 0}ms)`;
            } else {
                backendDot.classList.add('disconnected');
                backendText.textContent = 'Disconnected';
            }
            
            // Update AI status
            const aiDot = document.getElementById('ai-dot');
            const aiText = document.getElementById('ai-text');
            
            if (health.gemini_connection === 'ok') {
                aiDot.classList.remove('disconnected');
                aiText.textContent = 'Gemini Ready';
            } else {
                aiDot.classList.add('disconnected');
                aiText.textContent = 'Gemini Error';
            }
            
        } else {
            throw new Error(response.error);
        }
        
    } catch (error) {
        console.error('Error checking connection:', error);
        
        // Show all disconnected
        document.getElementById('backend-dot').classList.add('disconnected');
        document.getElementById('backend-text').textContent = 'Connection Error';
        document.getElementById('ai-dot').classList.add('disconnected');
        document.getElementById('ai-text').textContent = 'AI Unavailable';
    }
}

// Show success message
function showSuccessMessage(message) {
    showMessage(message, 'success');
}

// Show error message
function showError(message) {
    showMessage(message, 'error');
}

// Show message with type
function showMessage(message, type = 'info') {
    // Create message element
    const messageElement = document.createElement('div');
    const bgColor = type === 'success' ? 'rgba(72, 187, 120, 0.9)' : 
                   type === 'error' ? 'rgba(245, 101, 101, 0.9)' : 
                   'rgba(66, 153, 225, 0.9)';
    
    messageElement.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        right: 10px;
        background: ${bgColor};
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 13px;
        z-index: 1000;
        text-align: center;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;
    
    const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    messageElement.textContent = `${icon} ${message}`;
    
    document.body.appendChild(messageElement);
    
    // Animate in
    messageElement.style.transform = 'translateY(-20px)';
    messageElement.style.opacity = '0';
    
    setTimeout(() => {
        messageElement.style.transition = 'all 0.3s ease';
        messageElement.style.transform = 'translateY(0)';
        messageElement.style.opacity = '1';
    }, 10);
    
    // Remove after 3 seconds
    setTimeout(() => {
        messageElement.style.transform = 'translateY(-20px)';
        messageElement.style.opacity = '0';
        setTimeout(() => {
            messageElement.remove();
        }, 300);
    }, 3000);
}

// Enhanced CSS for page info section
const additionalCSS = `
    .page-info-section {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 16px;
        backdrop-filter: blur(10px);
    }
    
    .page-info-header h3 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
    }
    
    .page-details {
        font-size: 12px;
    }
    
    .page-title {
        color: white;
        font-weight: 500;
        margin-bottom: 4px;
        line-height: 1.3;
    }
    
    .page-domain {
        color: rgba(255, 255, 255, 0.7);
        font-size: 11px;
    }
    
    .loading {
        opacity: 0.7;
        pointer-events: none;
    }
`;

// Inject additional CSS
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);